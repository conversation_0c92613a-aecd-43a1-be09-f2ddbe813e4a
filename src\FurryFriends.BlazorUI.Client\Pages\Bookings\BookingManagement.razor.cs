using FurryFriends.BlazorUI.Client.Models.Bookings;
using FurryFriends.BlazorUI.Client.Models.Clients;
using FurryFriends.BlazorUI.Client.Services.Interfaces;
using Microsoft.AspNetCore.Components;

namespace FurryFriends.BlazorUI.Client.Pages.Bookings;

public partial class BookingManagement
{
  [Inject] public ILogger<BookingManagement> Logger { get; set; } = default!;
  [Inject] public IBookingService BookingService { get; set; } = default!;
  [Inject] public IClientService ClientService { get; set; } = default!;
  [Inject] public NavigationManager Navigation { get; set; } = default!;

  [Parameter] public Guid? ClientId { get; set; }

  private List<PetWalkerSummaryDto>? availablePetWalkers;
  private PetWalkerSummaryDto? selectedPetWalker;
  private ClientDto? clientInfo;
  private bool showBookingForm = false;
  private bool isLoadingPetWalkers = false;
  private string? errorMessage;

  // Success modal state
  private bool showSuccessModal = false;
  private BookingResponseDto? completedBooking;

  // Details modal state
  private bool showDetailsModal = false;
  private PetWalkerSummaryDto? selectedPetWalkerForDetails;

  protected override async Task OnInitializedAsync()
  {
    Logger.LogInformation("BookingManagement page initialized with ClientId: {ClientId}", ClientId);

    if (ClientId.HasValue)
    {
      await LoadClientInfoAsync(ClientId.Value);
      showBookingForm = true;
    }
    else
    {
      await LoadPetWalkersAsync();
    }
  }

  protected override async Task OnParametersSetAsync()
  {
    if (ClientId.HasValue && (clientInfo == null || clientInfo.Id != ClientId.Value))
    {
      await LoadClientInfoAsync(ClientId.Value);
      showBookingForm = true;
    }
  }

  private async Task LoadPetWalkersAsync()
  {
    try
    {
      isLoadingPetWalkers = true;
      errorMessage = null;
      StateHasChanged();

      Logger.LogInformation("Loading available pet walkers");

      var response = await BookingService.GetAvailablePetWalkersAsync();
      if (response.Success && response.Data != null)
      {
        availablePetWalkers = response.Data;
        Logger.LogInformation("Successfully loaded {Count} pet walkers", availablePetWalkers.Count);
      }
      else
      {
        availablePetWalkers = new List<PetWalkerSummaryDto>();
        errorMessage = response.Message ?? "Failed to load pet walkers";
        Logger.LogWarning("Failed to load pet walkers: {Error}", errorMessage);
      }
    }
    catch (Exception ex)
    {
      availablePetWalkers = new List<PetWalkerSummaryDto>();
      errorMessage = "An error occurred while loading pet walkers";
      Logger.LogError(ex, "Error loading pet walkers");
    }
    finally
    {
      isLoadingPetWalkers = false;
      StateHasChanged();
    }
  }

  private async Task LoadClientInfoAsync(Guid clientId)
  {
    try
    {
      Logger.LogInformation("Loading client info: {ClientId}", clientId);

      var response = await ClientService.GetClientAsync(clientId);
      if (response.Success && response.Data != null)
      {
        clientInfo = ClientService.MapClientDataToDto(response.Data);
        Logger.LogInformation("Successfully loaded client: {ClientName}", response.Data.Name);
      }
      else
      {
        errorMessage = response.Message ?? "Failed to load client information";
        Logger.LogWarning("Failed to load client {ClientId}: {Error}", clientId, errorMessage);
      }
    }
    catch (Exception ex)
    {
      errorMessage = "An error occurred while loading client information";
      Logger.LogError(ex, "Error loading client {ClientId}", clientId);
    }
  }

  private void StartNewBooking()
  {
    Logger.LogInformation("Starting new booking process");
    showBookingForm = false;
    selectedPetWalker = null;
    ClientId = null;
    Navigation.NavigateTo("/bookings/new");
  }

  private void SelectPetWalker(PetWalkerSummaryDto petWalker)
  {
    try
    {
      Logger.LogInformation("Pet walker selected: {PetWalkerId} - {PetWalkerName}", petWalker.Id, petWalker.FullName);

      // Toggle selection - if same pet walker is clicked, deselect
      if (selectedPetWalker?.Id == petWalker.Id)
      {
        selectedPetWalker = null;
      }
      else
      {
        selectedPetWalker = petWalker;
      }

      StateHasChanged();
    }
    catch (Exception ex)
    {
      Logger.LogError(ex, "Error selecting pet walker: {PetWalkerId}", petWalker.Id);
    }
  }

  private void ViewPetWalkerDetails(PetWalkerSummaryDto petWalker)
  {
    try
    {
      Logger.LogInformation("Viewing pet walker details: {PetWalkerId} - {PetWalkerName}", petWalker.Id, petWalker.FullName);
      
      selectedPetWalkerForDetails = petWalker;
      showDetailsModal = true;
      StateHasChanged();
    }
    catch (Exception ex)
    {
      Logger.LogError(ex, "Error viewing pet walker details: {PetWalkerId}", petWalker.Id);
    }
  }

  private void CloseDetailsModal()
  {
    showDetailsModal = false;
    selectedPetWalkerForDetails = null;
    StateHasChanged();
  }

  private void SelectPetWalkerFromDetails(PetWalkerSummaryDto petWalker)
  {
    try
    {
      Logger.LogInformation("Selecting pet walker from details modal: {PetWalkerId} - {PetWalkerName}", petWalker.Id, petWalker.FullName);
      
      selectedPetWalker = petWalker;
      CloseDetailsModal();
      StateHasChanged();
    }
    catch (Exception ex)
    {
      Logger.LogError(ex, "Error selecting pet walker from details: {PetWalkerId}", petWalker.Id);
    }
  }

  private void ProceedToBooking()
  {
    try
    {
      if (selectedPetWalker == null)
      {
        Logger.LogWarning("Attempted to proceed to booking without selected pet walker");
        return;
      }

      Logger.LogInformation("Proceeding to booking with pet walker: {PetWalkerId} - {PetWalkerName}", 
        selectedPetWalker.Id, selectedPetWalker.FullName);

      showBookingForm = true;
      StateHasChanged();

      // Update URL to reflect selected pet walker
      Navigation.NavigateTo($"/bookings/new?petWalkerId={selectedPetWalker.Id}");
    }
    catch (Exception ex)
    {
      Logger.LogError(ex, "Error proceeding to booking");
    }
  }

  private void ChangePetWalker()
  {
    Logger.LogInformation("User requested to change pet walker");
    showBookingForm = false;
    selectedPetWalker = null;
    Navigation.NavigateTo("/bookings/new");
  }

  private Task OnBookingCompleted(BookingResponseDto bookingResponse)
  {
    try
    {
      Logger.LogInformation("Booking completed successfully: {BookingId}", bookingResponse.BookingId);

      completedBooking = bookingResponse;
      showSuccessModal = true;
      StateHasChanged();

      return Task.CompletedTask;
    }
    catch (Exception ex)
    {
      Logger.LogError(ex, "Error handling booking completion");
      return Task.CompletedTask;
    }
  }

  private Task OnBookingCancelled()
  {
    try
    {
      Logger.LogInformation("Booking cancelled by user");

      // Navigate back to client selection or main bookings page
      if (ClientId.HasValue)
      {
        Navigation.NavigateTo("/bookings");
      }
      else
      {
        showBookingForm = false;
        StateHasChanged();
      }

      return Task.CompletedTask;
    }
    catch (Exception ex)
    {
      Logger.LogError(ex, "Error handling booking cancellation");
      return Task.CompletedTask;
    }
  }

  private void CloseSuccessModal()
  {
    showSuccessModal = false;
    completedBooking = null;
    StateHasChanged();
  }

  private void CreateAnotherBooking()
  {
    Logger.LogInformation("User requested to create another booking");

    CloseSuccessModal();

    // Keep the same pet walker if one was selected
    if (selectedPetWalker != null)
    {
      showBookingForm = true;
    }
    else
    {
      showBookingForm = false;
    }

    StateHasChanged();
  }

  private void NavigateToPetWalkers()
  {
    Logger.LogInformation("Navigating to pet walkers management");
    Navigation.NavigateTo("/petwalkers");
  }
}
