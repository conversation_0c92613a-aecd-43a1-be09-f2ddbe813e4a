@page "/bookings"
@page "/bookings/new"
@rendermode InteractiveAuto
@using FurryFriends.BlazorUI.Client.Components.Bookings
@using FurryFriends.BlazorUI.Client.Models.Bookings
@using FurryFriends.BlazorUI.Client.Models.Clients
@using FurryFriends.BlazorUI.Client.Services.Interfaces
@using Microsoft.Extensions.Logging

@inject ILogger<BookingManagement> _logger
@inject IClientService _clientService
@inject NavigationManager _navigation

<PageTitle>Booking Management - FurryFriends</PageTitle>

<div class="booking-management-container">
    <div class="page-header">
        <div class="header-content">
            <h1>
                <i class="fas fa-calendar-plus"></i>
                @if (showBookingForm)
                {
                    <span>Create New Booking</span>
                }
                else
                {
                    <span>Booking Management</span>
                }
            </h1>
            <p class="page-description">
                @if (showBookingForm)
                {
                    <span>Book a professional pet walker for your furry friend</span>
                }
                else
                {
                    <span>Manage your pet walking bookings and schedule new appointments</span>
                }
            </p>
        </div>
        
        @if (!showBookingForm)
        {
            <div class="header-actions">
                <button class="btn btn-primary" @onclick="StartNewBooking">
                    <i class="fas fa-plus"></i>
                    New Booking
                </button>
            </div>
        }
    </div>

    @if (showBookingForm)
    {
        <!-- Booking Form Section -->
        <div class="booking-form-section">
            @if (selectedPetWalker != null)
            {
                <div class="petwalker-info-banner">
                    <div class="petwalker-info">
                        <i class="fas fa-walking"></i>
                        <span>Selected Pet Walker: <strong>@selectedPetWalker.FullName</strong></span>
                        <small>(@selectedPetWalker.Email)</small>
                        @if (clientInfo != null)
                        {
                            <span class="client-context"> - Booking for: <strong>@clientInfo.FullName</strong></span>
                        }
                    </div>
                    <button class="btn btn-sm btn-outline-secondary" @onclick="ChangePetWalker">
                        <i class="fas fa-exchange-alt"></i>
                        Change Pet Walker
                    </button>
                </div>
            }

            <BookingFormComponent
                ClientId="@ClientId"
                @* ServiceArea="@selectedServiceArea" *@
                OnBookingCompleted="OnBookingCompleted"
                OnBookingCancelled="OnBookingCancelled" />
        </div>
    }
    else
    {
        <!-- Pet Walker Selection Section -->
        <div class="petwalker-selection-section">
            @if (isLoadingPetWalkers)
            {
                <div class="loading-container">
                    <div class="loading-spinner"></div>
                    <p>Loading available pet walkers...</p>
                </div>
            }
            else if (!string.IsNullOrEmpty(errorMessage))
            {
                <div class="error-container">
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span>@errorMessage</span>
                        <button class="btn btn-sm btn-outline-danger" @onclick="LoadPetWalkersAsync">
                            <i class="fas fa-redo"></i>
                            Retry
                        </button>
                    </div>
                </div>
            }
            else if (availablePetWalkers == null || !availablePetWalkers.Any())
            {
                <div class="no-petwalkers-container">
                    <div class="empty-state">
                        <i class="fas fa-walking"></i>
                        <h3>No Pet Walkers Available</h3>
                        <p>There are currently no pet walkers available for booking.</p>
                        <button class="btn btn-primary" @onclick="NavigateToPetWalkers">
                            <i class="fas fa-user-plus"></i>
                            Manage Pet Walkers
                        </button>
                    </div>
                </div>
            }
            else
            {
                <div class="petwalkers-grid">
                    <h3>Select a Pet Walker</h3>
                    <p class="section-description">Choose a professional pet walker for your furry friend</p>

                    <div class="petwalkers-list">
                        @foreach (var petWalker in availablePetWalkers)
                        {
                            <div class="petwalker-card modern @(selectedPetWalker?.Id == petWalker.Id ? "selected" : "")" 
                                 @onclick="() => SelectPetWalker(petWalker)">
                                <div class="card-selection-indicator">
                                    @if (selectedPetWalker?.Id == petWalker.Id)
                                    {
                                        <i class="fas fa-check-circle"></i>
                                    }
                                </div>
                                <div class="card-media">
                                    @if (!string.IsNullOrWhiteSpace(petWalker.BioPicture?.Uri))
                                    {
                                        <img class="avatar" src="@petWalker.BioPicture!.Uri" alt="@($"{petWalker.FullName} profile")" />
                                    }
                                    else
                                    {
                                        <div class="avatar-fallback">
                                            <i class="fas fa-user"></i>
                                        </div>
                                    }
                                </div>
                                <div class="card-body">
                                    <div class="card-header">
                                        <h4>@petWalker.FullName</h4>
                                        <div class="rating">
                                            @{
                                                var fullStars = (int)Math.Floor(petWalker.Rating);
                                                var hasHalf = (petWalker.Rating - fullStars) >= 0.5;
                                                var empty = 5 - fullStars - (hasHalf ? 1 : 0);
                                            }
                                            <div class="stars">
                                                @for (var i = 0; i < fullStars; i++)
                                                {
                                                    <i class="fas fa-star"></i>
                                                }
                                                @if (hasHalf)
                                                {
                                                    <i class="fas fa-star-half-alt"></i>
                                                }
                                                @for (var i = 0; i < empty; i++)
                                                {
                                                    <i class="far fa-star"></i>
                                                }
                                            </div>
                                            <span class="rating-value">@petWalker.Rating.ToString("0.0")</span>
                                            <span class="review-count">(@petWalker.ReviewCount)</span>
                                        </div>
                                    </div>

                                    <div class="meta">
                                        <div class="meta-item">
                                            <span class="rate"><i class="fas fa-dollar-sign"></i>@petWalker.HourlyRate.ToString("F2")/hr</span>
                                        </div>
                                        @if (petWalker.YearsOfExperience > 0)
                                        {
                                            <div class="meta-item">
                                                <span class="experience"><i class="fas fa-briefcase"></i>@petWalker.YearsOfExperience yr@(petWalker.YearsOfExperience > 1 ? "s" : "") exp</span>
                                            </div>
                                        }
                                        <div class="meta-item email-item">
                                            <span class="email" title="@petWalker.Email"><i class="fas fa-envelope"></i>@petWalker.Email</span>
                                        </div>
                                    </div>

                                    @if (petWalker.ServiceAreas.Any())
                                    {
                                        <div class="service-areas">
                                            @foreach (var area in petWalker.ServiceAreas.Take(3))
                                            {
                                                <span class="chip"><i class="fas fa-map-marker-alt"></i>@area</span>
                                            }
                                            @if (petWalker.ServiceAreas.Count > 3)
                                            {
                                                <span class="chip more">+@((petWalker.ServiceAreas.Count) - 3)</span>
                                            }
                                        </div>
                                    }
                                </div>
                                <div class="card-actions">
                                    <button class="btn btn-sm btn-outline-primary view-details-btn" 
                                            @onclick:stopPropagation="true"
                                            @onclick="() => ViewPetWalkerDetails(petWalker)">
                                        <i class="fas fa-info-circle"></i>
                                        Details
                                    </button>
                                    @if (selectedPetWalker?.Id == petWalker.Id)
                                    {
                                        <button class="btn btn-sm btn-primary book-now-btn" 
                                                @onclick:stopPropagation="true"
                                                @onclick="() => ProceedToBooking()">
                                            <i class="fas fa-calendar-plus"></i>
                                            Book Now
                                        </button>
                                    }
                                </div>
                            </div>
                        }
                    </div>
                </div>
            }
        </div>
    }

    <!-- Pet Walker Details Modal -->
    @if (showDetailsModal && selectedPetWalkerForDetails != null)
    {
        <div class="modal-overlay" @onclick="CloseDetailsModal">
            <div class="modal-content details-modal" @onclick:stopPropagation="true">
                <div class="modal-header">
                    <div class="petwalker-avatar">
                        @if (!string.IsNullOrWhiteSpace(selectedPetWalkerForDetails.BioPicture?.Uri))
                        {
                            <img src="@selectedPetWalkerForDetails.BioPicture!.Uri" alt="@($"{selectedPetWalkerForDetails.FullName} profile")" />
                        }
                        else
                        {
                            <div class="avatar-fallback">
                                <i class="fas fa-user"></i>
                            </div>
                        }
                    </div>
                    <div class="petwalker-info">
                        <h3>@selectedPetWalkerForDetails.FullName</h3>
                        <div class="rating">
                            @{
                                var fullStars = (int)Math.Floor(selectedPetWalkerForDetails.Rating);
                                var hasHalf = (selectedPetWalkerForDetails.Rating - fullStars) >= 0.5;
                                var empty = 5 - fullStars - (hasHalf ? 1 : 0);
                            }
                            <div class="stars">
                                @for (var i = 0; i < fullStars; i++)
                                {
                                    <i class="fas fa-star"></i>
                                }
                                @if (hasHalf)
                                {
                                    <i class="fas fa-star-half-alt"></i>
                                }
                                @for (var i = 0; i < empty; i++)
                                {
                                    <i class="far fa-star"></i>
                                }
                            </div>
                            <span class="rating-value">@selectedPetWalkerForDetails.Rating.ToString("0.0")</span>
                            <span class="review-count">(@selectedPetWalkerForDetails.ReviewCount reviews)</span>
                        </div>
                    </div>
                    <button class="btn btn-sm btn-outline-secondary close-btn" @onclick="CloseDetailsModal">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="details-grid">
                        <div class="detail-item">
                            <i class="fas fa-dollar-sign"></i>
                            <span class="label">Hourly Rate:</span>
                            <span class="value">$@selectedPetWalkerForDetails.HourlyRate.ToString("F2")</span>
                        </div>
                        @if (selectedPetWalkerForDetails.YearsOfExperience > 0)
                        {
                            <div class="detail-item">
                                <i class="fas fa-briefcase"></i>
                                <span class="label">Experience:</span>
                                <span class="value">@selectedPetWalkerForDetails.YearsOfExperience year@(selectedPetWalkerForDetails.YearsOfExperience > 1 ? "s" : "")</span>
                            </div>
                        }
                        <div class="detail-item">
                            <i class="fas fa-envelope"></i>
                            <span class="label">Email:</span>
                            <span class="value">@selectedPetWalkerForDetails.Email</span>
                        </div>
                    </div>
                    @if (selectedPetWalkerForDetails.ServiceAreas.Any())
                    {
                        <div class="service-areas-section">
                            <h4><i class="fas fa-map-marker-alt"></i> Service Areas</h4>
                            <div class="service-areas-list">
                                @foreach (var area in selectedPetWalkerForDetails.ServiceAreas)
                                {
                                    <span class="chip">@area</span>
                                }
                            </div>
                        </div>
                    }
                </div>
                <div class="modal-actions">
                    <button class="btn btn-primary" @onclick="() => SelectPetWalkerFromDetails(selectedPetWalkerForDetails)">
                        <i class="fas fa-check"></i>
                        Select This Pet Walker
                    </button>
                    <button class="btn btn-secondary" @onclick="CloseDetailsModal">
                        <i class="fas fa-times"></i>
                        Close
                    </button>
                </div>
            </div>
        </div>
    }

    <!-- Success Modal -->
    @if (showSuccessModal)
    {
        <div class="modal-overlay" @onclick="CloseSuccessModal">
            <div class="modal-content success-modal" @onclick:stopPropagation="true">
                <div class="modal-header">
                    <div class="success-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h3>Booking Created Successfully!</h3>
                </div>
                <div class="modal-body">
                    @if (completedBooking != null)
                    {
                        <p>Your booking has been created with ID: <strong>@completedBooking.BookingId</strong></p>
                        <p>Date: <strong>@completedBooking.StartDate.ToString("dddd, MMMM dd, yyyy")</strong></p>
                        <p>Time: <strong>@completedBooking.StartDate.ToString("HH:mm") - @completedBooking.EndDate.ToString("HH:mm")</strong></p>
                    }
                    <p class="success-message">@completedBooking?.Message</p>
                </div>
                <div class="modal-actions">
                    <button class="btn btn-primary" @onclick="CreateAnotherBooking">
                        <i class="fas fa-plus"></i>
                        Create Another Booking
                    </button>
                    <button class="btn btn-secondary" @onclick="CloseSuccessModal">
                        <i class="fas fa-times"></i>
                        Close
                    </button>
                </div>
            </div>
        </div>
    }
</div>
